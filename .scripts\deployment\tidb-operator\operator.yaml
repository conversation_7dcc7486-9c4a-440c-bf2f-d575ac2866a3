apiVersion: apps/v1
kind: Deployment
metadata:
  name: tidb-controller-manager
  namespace: tidb-admin
  labels:
    app: tidb-controller-manager
spec:
  replicas: 1
  selector:
    matchLabels:
      app: tidb-controller-manager
  template:
    metadata:
      labels:
        app: tidb-controller-manager
    spec:
      serviceAccountName: tidb-controller-manager
      containers:
      - name: tidb-controller-manager
        image: pingcap/tidb-operator:v1.6.0
        command:
        - /usr/local/bin/tidb-controller-manager
        - -v=2
        - -cluster-scoped=true
        env:
        - name: NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: TZ
          value: UTC
        resources:
          limits:
            cpu: 500m
            memory: 500Mi
          requests:
            cpu: 200m
            memory: 250Mi
