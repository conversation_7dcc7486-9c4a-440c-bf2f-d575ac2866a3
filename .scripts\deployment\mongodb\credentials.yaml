# MongoDB Credentials Configuration
# Generated on: 2025-05-29

# Root Admin Credentials
MONGODB_ROOT_USERNAME: admin
MONGODB_ROOT_PASSWORD: Mg8Kx9Pq2Nv7Wr5Zt3Bm6Lp4Hj1Qw8

# Application Database Credentials  
MONGODB_DATABASE: cm_app_db
MONGODB_USERNAME: cm_app_user
MONGODB_PASSWORD: Yx4Kp9Mq7Nv2Wr8Zt6Bm3Lp1Hj5Qw9

# Replica Set Configuration
MONGODB_REPLICA_SET_NAME: rs0

# Connection Strings
MONGODB_CONNECTION_STRING_INTERNAL: mongodb://cm_app_user:<EMAIL>:27017/cm_app_db
MONGODB_CONNECTION_STRING_ADMIN: mongodb://admin:<EMAIL>:27017/admin

# External Access (NodePort)
MONGODB_EXTERNAL_PORT: 32017

# Notes:
# - Change these passwords before production use
# - Store these credentials securely
# - Use Kubernetes secrets for sensitive data
# - Enable authentication and SSL/TLS for production
