import type { ServerWebSocket } from "bun";
import { peers, type ServerWSData } from "./srv/core/ws.handler/ws.handler";
import { $buildWSHandler } from "./srv/core/ws.handler/ws.handlers";
import "./srv/lib/index";
import { cDeserialize, type M } from "cm_net";
export const PORT = 9494;

const server = Bun.serve({
    hostname: "0.0.0.0",
    port: PORT,
    async fetch(req) {
        const url = new URL(req.url);
        const t = url.pathname.split("/");
        const [, type, ...args] = t;
        console.log(req.method, url.pathname);

        if (type === "peers") {
            const peerInfo: M[] = [];

            Object.entries(peers).forEach(([sid, sidPeers]) => {
                const peers = [] as M[];
                sidPeers.forEach((peer) => {
                    peers.push({
                        name: peer.vars.get('name'),
                        deviceType: peer.vars.get('deviceType'),
                        shardId: peer.shardId,
                        connectedAt: peer.connectedAt,
                        lastActivityAt: peer.lastActivityAt,
                        sessionLength:
                            Math.round((peer.lastActivityAt.getTime() -
                                peer.connectedAt.getTime()) / 1000) + "s",
                        uid: peer.user.uid,
                        sysUsage: peer.sysUsage,
                    });
                });
                peerInfo.push({
                    sid,
                    peers,
                });
            });

            return cors(
                new Response(JSON.stringify(peerInfo, null, 2), {
                    headers: {
                        "Content-Type": "application/json",
                    },
                }),
            );
        }

        switch (type) {
            case "ws": {
                server.upgrade(req, {
                    data: {
                        handlerType: type,
                        url: url,
                        headers: req.headers,
                    },
                } as any);

                return undefined;
            }

            default:
                return new Response("Alien AI Gateway");
        }
    },

    websocket: {
        async open(ws) {
            ws.data = {
                url: (ws.data as any)!.url,
                headers: (ws.data as any)!.headers,
            } as any;

            try {
                $buildWSHandler(ws as ServerWebSocket<ServerWSData>);
            } catch (ex) {
                console.log("Error building ws handler", ex);
            }
        },
        async message(ws, message) {
            if (typeof message !== "string") {
                return console.log(
                    "WARN: message must be string:",
                    typeof message,
                    message,
                );
            }

            let segments;
            try {
                segments = cDeserialize(JSON.parse(message));
            } catch {
                return l("Cannot parse msg:", message);
            }

            if (!Array.isArray(segments)) {
                return l("Message not array:", segments);
            }

            const [cmd, ...args] = segments;

            // forward message to handler
            const handler = (ws.data as ServerWSData).handler;
            if (!handler) {
                return console.log("WARN: no handler for message", cmd, args);
            }
            await handler.ready;

            try {
                handler.handleMsg(cmd, args);
            } catch (ex) {
                console.log("Error handling message", cmd, args, ex);
            }
        },
        async close(ws, code, reason) {
            const sid = (ws.data as ServerWSData).handler?.sid;
            console.log("Disconnected:", `${sid}:${code} ${reason}`);
            (ws.data as ServerWSData).handler?.handleClose();
        },
    },
});

export function cors(res: Response) {
    res.headers.set("Access-Control-Allow-Origin", "*");
    res.headers.set(
        "Access-Control-Allow-Methods",
        "GET, POST, PUT, DELETE, OPTIONS",
    );
    return res;
}

console.clear();
console.log("CM Gateway Server listening port", PORT);
