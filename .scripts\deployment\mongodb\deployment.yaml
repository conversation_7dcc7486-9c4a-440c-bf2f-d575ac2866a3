apiVersion: apps/v1
kind: Deployment
metadata:
  name: mongodb
  namespace: mongodb
  labels:
    app: mongodb
spec:
  replicas: 1
  selector:
    matchLabels:
      app: mongodb
  template:
    metadata:
      labels:
        app: mongodb
    spec:
      containers:
      - name: mongodb
        image: mongo:7.0
        ports:
        - containerPort: 27017
          name: mongodb
        env:
        - name: MONGO_INITDB_ROOT_USERNAME
          valueFrom:
            secretKeyRef:
              name: mongodb-secret
              key: mongodb-root-username
        - name: MONGO_INITDB_ROOT_PASSWORD
          valueFrom:
            secretKeyRef:
              name: mongodb-secret
              key: mongodb-root-password
        - name: MONGO_INITDB_DATABASE
          valueFrom:
            secretKeyRef:
              name: mongodb-secret
              key: mongodb-database
        volumeMounts:
        - name: mongodb-data
          mountPath: /data/db
        - name: mongodb-config-volume
          mountPath: /etc/mongod.conf
          subPath: mongod.conf
        - name: mongodb-config-volume
          mountPath: /docker-entrypoint-initdb.d/init-mongo.js
          subPath: init-mongo.js
        - name: mongodb-logs
          mountPath: /var/log/mongodb
        - name: mongodb-run
          mountPath: /var/run/mongodb
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
        livenessProbe:
          exec:
            command:
            - mongosh
            - --eval
            - "db.adminCommand('ping')"
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          exec:
            command:
            - mongosh
            - --eval
            - "db.adminCommand('ping')"
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
      volumes:
      - name: mongodb-data
        persistentVolumeClaim:
          claimName: mongodb-data
      - name: mongodb-config-volume
        configMap:
          name: mongodb-config
          defaultMode: 0755
      - name: mongodb-logs
        emptyDir: {}
      - name: mongodb-run
        emptyDir: {}
      restartPolicy: Always
