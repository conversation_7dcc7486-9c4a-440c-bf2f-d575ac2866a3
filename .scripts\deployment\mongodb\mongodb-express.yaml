apiVersion: apps/v1
kind: Deployment
metadata:
  name: mongodb-express
  namespace: mongodb
  labels:
    app: mongodb-express
spec:
  replicas: 1
  selector:
    matchLabels:
      app: mongodb-express
  template:
    metadata:
      labels:
        app: mongodb-express
    spec:
      containers:
      - name: mongodb-express
        image: mongo-express:1.0.2
        ports:
        - containerPort: 8081
          name: http
        env:
        - name: ME_CONFIG_MONGODB_SERVER
          value: "mongodb.mongodb.svc.cluster.local"
        - name: ME_CONFIG_MONGODB_PORT
          value: "27017"
        - name: ME_CONFIG_MONGODB_ADMINUSERNAME
          valueFrom:
            secretKeyRef:
              name: mongodb-secret
              key: mongodb-root-username
        - name: ME_CONFIG_MONGODB_ADMINPASSWORD
          valueFrom:
            secretKeyRef:
              name: mongodb-secret
              key: mongodb-root-password
        - name: ME_CONFIG_BASICAUTH_USERNAME
          value: "admin"
        - name: ME_CONF<PERSON>_BASICAUTH_PASSWORD
          value: "MongoExpress123!"
        - name: ME_CONFIG_MONGODB_ENABLE_ADMIN
          value: "true"
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /
            port: 8081
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /
            port: 8081
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: mongodb-express
  namespace: mongodb
  labels:
    app: mongodb-express
spec:
  type: ClusterIP
  ports:
  - port: 8081
    targetPort: 8081
    protocol: TCP
    name: http
  selector:
    app: mongodb-express
---
apiVersion: v1
kind: Service
metadata:
  name: mongodb-express-nodeport
  namespace: mongodb
  labels:
    app: mongodb-express
spec:
  type: NodePort
  ports:
  - port: 8081
    targetPort: 8081
    protocol: TCP
    name: http
    nodePort: 32081  # External access via node IP:32081
  selector:
    app: mongodb-express
