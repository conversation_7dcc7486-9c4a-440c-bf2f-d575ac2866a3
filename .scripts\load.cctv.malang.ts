import { log } from "node:console";
import { Device } from "cm_net";
import { gatewayStore } from "../instance";

const cameraUrl = "http://api.cctv.malangkota.go.id/records/cameras";

export async function $cityCCTVPut(cityCCTV: Device) {
    gatewayStore.sdb.put("cctv", cityCCTV, {
        txt: cityCCTV.name + " " + (cityCCTV?.location?.txt ?? ""),
    });
}

export async function loadCCtvMalangKota() {
    const res = await fetch(cameraUrl);
    const data = await res.json();

    const records = data.records;
    let i = 0;
    const count = records.length;
    for (const record of records) {
        i++;
        const streamId = record.stream_id;
        if (!streamId) {
            console.log("No StreamID: ", record.name);
            return;
        }

        const streamUrl =
            `http://stream.cctv.malangkota.go.id/WebRTCApp/play.html?name=${record.stream_id}`;

        log(i, "/", count, "Stored CCTV", record.name);

        await $cityCCTVPut({
            id: record.id,
            name: record.name,
            type: "cctv",
            location: {
                city: "malang",

                lat: record.latitude,
                lng: record.longitude,
                txt: record.address + " - Kota Malang",
            },

            streamUrl: streamUrl,
        });
    }

    console.log("Done Upload CCTV Meta");
}

await loadCCtvMalangKota();
