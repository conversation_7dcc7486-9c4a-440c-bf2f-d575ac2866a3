#!/bin/bash
set -e

echo "🚀 Deploying MongoDB with Ceph persistent storage..."

# Create namespace
echo "📁 Creating MongoDB namespace..."
kubectl apply -f .scripts/deployment/mongodb/namespace.yaml

# Create secrets
echo "🔐 Creating MongoDB secrets..."
kubectl apply -f .scripts/deployment/mongodb/secret.yaml

# Create ConfigMap
echo "⚙️ Creating MongoDB configuration..."
kubectl apply -f .scripts/deployment/mongodb/configmap.yaml

# Create PVCs with Ceph storage
echo "💾 Creating persistent volume claims with Ceph storage..."
kubectl apply -f .scripts/deployment/mongodb/pvc.yaml

# Wait for PVCs to be bound
echo "⏳ Waiting for PVCs to be bound..."
kubectl wait --for=condition=Bound pvc/mongodb-data -n mongodb --timeout=300s
kubectl wait --for=condition=Bound pvc/mongodb-config -n mongodb --timeout=300s

# Deploy MongoDB
echo "🗄️ Deploying MongoDB..."
kubectl apply -f .scripts/deployment/mongodb/deployment.yaml

# Create services
echo "🌐 Creating MongoDB services..."
kubectl apply -f .scripts/deployment/mongodb/service.yaml

# Wait for deployment to be ready
echo "⏳ Waiting for MongoDB deployment to be ready..."
kubectl wait --for=condition=Available deployment/mongodb -n mongodb --timeout=300s

echo "✅ MongoDB deployment completed!"
echo ""
echo "📊 Deployment Status:"
kubectl get pods -n mongodb
echo ""
kubectl get pvc -n mongodb
echo ""
kubectl get svc -n mongodb
echo ""
echo "🔗 Access Information:"
echo "- Internal cluster access: mongodb://mongodb.mongodb.svc.cluster.local:27017"
echo "- NodePort access: mongodb://<node-ip>:32017"
echo ""
echo "🔐 Credentials (from credentials.yaml):"
echo "- Root user: admin"
echo "- Root password: Mg8Kx9Pq2Nv7Wr5Zt3Bm6Lp4Hj1Qw8"
echo "- App user: cm_app_user"
echo "- App password: Yx4Kp9Mq7Nv2Wr8Zt6Bm3Lp1Hj5Qw9"
echo "- Database: cm_app_db"
echo ""
echo "🧪 Test the connection:"
echo "kubectl exec -it deployment/mongodb -n mongodb -- mongosh -u admin -p Mg8Kx9Pq2Nv7Wr5Zt3Bm6Lp4Hj1Qw8 --authenticationDatabase admin"
echo ""
echo "📝 Connection strings:"
echo "- Admin: mongodb://admin:<EMAIL>:27017/admin"
echo "- App: mongodb://cm_app_user:<EMAIL>:27017/cm_app_db"
