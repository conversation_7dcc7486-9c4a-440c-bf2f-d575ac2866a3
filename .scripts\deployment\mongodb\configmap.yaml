apiVersion: v1
kind: ConfigMap
metadata:
  name: mongodb-config
  namespace: mongodb
data:
  mongod.conf: |
    # MongoDB Configuration File
    storage:
      dbPath: /data/db
      journal:
        enabled: true
      wiredTiger:
        engineConfig:
          cacheSizeGB: 2
    
    systemLog:
      destination: file
      logAppend: true
      path: /var/log/mongodb/mongod.log
      logRotate: reopen
    
    net:
      port: 27017
      bindIp: 0.0.0.0
    
    security:
      authorization: enabled
    
    replication:
      replSetName: rs0
    
    processManagement:
      fork: false
      pidFilePath: /var/run/mongodb/mongod.pid
  
  init-mongo.js: |
    // MongoDB Initialization Script
    print('Starting MongoDB initialization...');
    
    // Switch to admin database
    db = db.getSiblingDB('admin');
    
    // Create root user if not exists
    try {
      db.createUser({
        user: 'admin',
        pwd: 'Mg8Kx9Pq2Nv7Wr5Zt3Bm6Lp4Hj1Qw8',
        roles: [
          { role: 'root', db: 'admin' },
          { role: 'userAdminAnyDatabase', db: 'admin' },
          { role: 'dbAdminAnyDatabase', db: 'admin' },
          { role: 'readWriteAnyDatabase', db: 'admin' }
        ]
      });
      print('Root user created successfully');
    } catch (e) {
      print('Root user already exists or error: ' + e);
    }
    
    // Switch to application database
    db = db.getSiblingDB('cm_app_db');
    
    // Create application user
    try {
      db.createUser({
        user: 'cm_app_user',
        pwd: 'Yx4Kp9Mq7Nv2Wr8Zt6Bm3Lp1Hj5Qw9',
        roles: [
          { role: 'readWrite', db: 'cm_app_db' },
          { role: 'dbAdmin', db: 'cm_app_db' }
        ]
      });
      print('Application user created successfully');
    } catch (e) {
      print('Application user already exists or error: ' + e);
    }
    
    // Create sample collection and document
    db.test_collection.insertOne({
      message: 'MongoDB is ready!',
      timestamp: new Date(),
      version: '1.0'
    });
    
    print('MongoDB initialization completed successfully');
  
  docker-entrypoint.sh: |
    #!/bin/bash
    set -e
    
    # Start MongoDB in background
    mongod --config /etc/mongod.conf &
    MONGO_PID=$!
    
    # Wait for MongoDB to start
    echo "Waiting for MongoDB to start..."
    until mongosh --eval "print('MongoDB is ready')" > /dev/null 2>&1; do
      sleep 2
    done
    
    # Run initialization script
    echo "Running initialization script..."
    mongosh /docker-entrypoint-initdb.d/init-mongo.js
    
    # Wait for MongoDB process
    wait $MONGO_PID
