apiVersion: v1
kind: Service
metadata:
  name: mongodb
  namespace: mongodb
  labels:
    app: mongodb
spec:
  type: ClusterIP
  ports:
  - port: 27017
    targetPort: 27017
    protocol: TCP
    name: mongodb
  selector:
    app: mongodb
---
apiVersion: v1
kind: Service
metadata:
  name: mongodb-nodeport
  namespace: mongodb
  labels:
    app: mongodb
spec:
  type: NodePort
  ports:
  - port: 27017
    targetPort: 27017
    protocol: TCP
    name: mongodb
    nodePort: 32017  # External access via node IP:32017
  selector:
    app: mongodb
